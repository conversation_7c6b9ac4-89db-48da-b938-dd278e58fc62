import { ref } from 'vue'
import { defineStore } from 'pinia'
import WebSocketConnectMethod from '@/utils/wsconnecter'
import { formatDate } from '@/utils'
export const useSocketStore = defineStore('socket', () => {
  // const socket = new WebSocketConnectMethod('ws://192.168.1.223:10096/')
  const socket = new WebSocketConnectMethod('ws://192.168.1.224:10096/')
  const state = ref(0) // 0: 连接关闭, 1: 连接成功, 2: 连接失败
  const lastData = ref({})
  function wsStart() {
    socket.wsStart()
  }

  function stateHandle(value) {
    state.value = value
  }

  function msgHandle(value) {
    lastData.value = value
    console.log(
      '收到消息:',
      value.text,
      '对话状态:',
      value.isFinish,
      '消息时间:',
      formatDate(new Date())
    )
  }

  function wsSend(value) {
    socket.wsSend(value)
  }

  function clearSocketText() {
    lastData.value = {}
  }

  function resetRecognition() {
    socket.resetRecognition()
  }
  function resetMessage() {
    socket.resetMessage()
  }

  return {
    stateHandle,
    state,
    wsStart,
    wsSend,
    lastData,
    clearSocketText,
    resetRecognition,
    msgHandle,
    resetMessage
  }
})
