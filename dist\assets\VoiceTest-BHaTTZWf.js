import{_ as d,x as u,c as r,a as v,d as s,X as e,e as t,t as i}from"./index-BTJj10y7.js";const p={class:"voice-test"},_={class:"status-panel"},m={class:"status-item"},x={class:"status-item"},f={class:"status-item"},g={class:"value"},k={class:"text"},F={__name:"VoiceTest",setup(b){const o=u("wake"),{isKeyListening:a,isRecording:n,lastText:c}=o;return(B,l)=>(v(),r("div",p,[l[4]||(l[4]=s("div",{class:"header"},[s("h1",null,"按键语音监听测试"),s("p",null,"按住 F2 键开始语音监听，松开停止监听")],-1)),s("div",_,[s("div",m,[l[0]||(l[0]=s("span",{class:"label"},"按键状态:",-1)),s("span",{class:e(["value",{active:t(a)}])},i(t(a)?"按键按下":"按键松开"),3)]),s("div",x,[l[1]||(l[1]=s("span",{class:"label"},"录音状态:",-1)),s("span",{class:e(["value",{active:t(n)}])},i(t(n)?"正在录音":"录音停止"),3)]),s("div",f,[l[2]||(l[2]=s("span",{class:"label"},"最后识别文本:",-1)),s("span",g,i(t(c)||"暂无"),1)])]),l[5]||(l[5]=s("div",{class:"instructions"},[s("h3",null,"使用说明："),s("ul",null,[s("li",null,"按住 F2 键开始语音监听"),s("li",null,"松开 F2 键停止语音监听"),s("li",null,"监听过程中会显示实时状态"),s("li",null,"识别到的文本会显示在上方")])],-1)),s("div",{class:e(["visual-indicator",{listening:t(a)}])},[l[3]||(l[3]=s("div",{class:"circle"},[s("div",{class:"inner-circle"})],-1)),s("div",k,i(t(a)?"正在监听...":"按住 F2 开始"),1)],2)]))}},V=d(F,[["__scopeId","data-v-1d62c760"]]);export{V as default};
