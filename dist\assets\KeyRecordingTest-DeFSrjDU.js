import{u as r}from"./useListen-Drd5wNNB.js";import{_ as c,a4 as p,c as v,a as _,d as s,X as a,e as l,t as e,A as g}from"./index-BTJj10y7.js";const m={class:"key-recording-test"},b={class:"status-panel"},f={class:"status-item"},S={class:"status-item"},k={class:"value"},y={class:"status-item"},x={class:"value"},A={class:"status-item"},F={class:"value"},I={class:"text"},N={class:"debug-info"},T={class:"debug-item"},B={class:"debug-item"},D={__name:"KeyRecordingTest",setup(K){const{listenText:o,isKeyListening:n,aiResponse:d}=r(),i=p(),u=()=>{switch(i.state){case 0:return"连接关闭";case 1:return"连接成功";case 2:return"连接失败";default:return"未知状态"}};return(R,t)=>(_(),v("div",m,[t[8]||(t[8]=s("div",{class:"header"},[s("h1",null,"按键录音测试"),s("p",null,"按住 F2 键进行语音录制，松开后自动发送给 AI")],-1)),s("div",b,[s("div",f,[t[0]||(t[0]=s("span",{class:"label"},"按键状态:",-1)),s("span",{class:a(["value",{active:l(n)}])},e(l(n)?"按键按下":"按键松开"),3)]),s("div",S,[t[1]||(t[1]=s("span",{class:"label"},"实时识别文字:",-1)),s("span",k,e(l(o)||"暂无"),1)]),s("div",y,[t[2]||(t[2]=s("span",{class:"label"},"WebSocket原始数据:",-1)),s("span",x,[s("pre",null,e(JSON.stringify(l(i).lastData,null,2)),1)])]),s("div",A,[t[3]||(t[3]=s("span",{class:"label"},"AI 回复:",-1)),s("span",F,e(l(d)||"暂无"),1)])]),t[9]||(t[9]=s("div",{class:"instructions"},[s("h3",null,"测试说明："),s("ul",null,[s("li",null,"按住 F2 键开始录音"),s("li",null,"说话时会看到实时识别的文字"),s("li",null,"松开 F2 键后，系统会使用最终校验的文字发送给 AI"),s("li",null,"观察控制台日志，查看详细的处理过程")])],-1)),s("div",{class:a(["visual-indicator",{listening:l(n)}])},[t[4]||(t[4]=s("div",{class:"circle"},[s("div",{class:"inner-circle"})],-1)),s("div",I,e(l(n)?"正在录音...":"按住 F2 开始录音"),1)],2),s("div",N,[t[7]||(t[7]=s("h3",null,"调试信息：",-1)),s("div",T,[t[5]||(t[5]=s("strong",null,"WebSocket 状态:",-1)),g(" "+e(u()),1)]),s("div",B,[t[6]||(t[6]=s("strong",null,"最后收到的数据:",-1)),s("pre",null,e(JSON.stringify(l(i).lastData,null,2)),1)])])]))}},O=c(D,[["__scopeId","data-v-bafd5b7d"]]);export{O as default};
