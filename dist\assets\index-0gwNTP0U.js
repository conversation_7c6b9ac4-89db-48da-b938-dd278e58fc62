import{_ as T,u as C,s as J,m as O,x as B,r as l,w as p,o as E,c as _,a as g,d as a,k as F,t as u,e as M,E as V,h as W,j,v as H,F as L,G as R}from"./index-BTJj10y7.js";import{L as q,b as G}from"./case-CQdY-ynY.js";const z={class:"app-container"},K={class:"student-info"},Q={class:"ai-analysis-container"},U={class:"ai-analysis-content"},X={key:0,class:"loader-box"},Y={class:"cont-down-box"},Z={__name:"index",setup($){const v=C(),d=J(),f=JSON.parse(O()),{aiRequest:w,inAnalysisPage:I,resetAnalysisPage:h,recPause:y}=B("wake"),S=JSON.parse(window.localStorage.getItem("submitInfo")),n=l({});function x(){G({id:f.caseId}).then(e=>{n.value=e.data,D()})}const i=l("");async function D(){const e={病例名称:n.value.name,年龄:n.value.age,性别:n.value.sex==="F"?"女":"男",病例简介:n.value.mainDemands,学生问的问题:JSON.parse(S.record).map(o=>o.userProblem)},s=await w(JSON.stringify(e),R);b(s,10)}async function b(e,s){i.value="";let o=0;const P=setInterval(()=>{o<e.length?(i.value+=e.charAt(o),o++):clearInterval(P)},s)}const t=l(null);p(()=>I.value,e=>{e===0&&m()});function m(){t.value&&clearTimeout(t.value),h(),d.clearData(),window.localStorage.removeItem("submitInfo"),v.push("/")}function N(){t.value=setTimeout(()=>{m()},30*1e3)}const r=l(5),c=l(!1);p(()=>d.spTalkMessage,e=>{e&&(t.value&&(clearTimeout(t.value),t.value=null),y(),c.value=!0,k())});function k(){const e=setInterval(()=>{r.value--,r.value<=0&&(r.value=5,clearInterval(e),A())},1e3)}function A(){c.value=!1,v.push("/sp")}return E(()=>{x(),N()}),(e,s)=>(g(),_(L,null,[a("div",z,[a("div",K,"当前登录用户："+u(M(f).userInfo.studentName),1),a("div",Q,[s[0]||(s[0]=a("div",{class:"ai-analysis-header"},[a("img",{src:V,alt:""}),a("div",null,[a("h1",null,"AI病例训练分析"),a("p",null,"训练分析助你更快掌握病例")])],-1)),a("div",U,[a("pre",null,"            "+u(i.value)+`
        `,1),i.value?W("",!0):(g(),_("div",X,[j(q)]))])])]),F(a("div",Y,[s[1]||(s[1]=a("p",null,"进入病例开始训练",-1)),a("span",null,u(r.value)+"s",1)],512),[[H,c.value]])],64))}},se=T(Z,[["__scopeId","data-v-2768ba49"]]);export{se as default};
