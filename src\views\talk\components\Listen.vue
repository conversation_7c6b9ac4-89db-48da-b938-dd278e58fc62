<script setup>
const props = defineProps({
  text: {
    type: String,
    default: ''
  }
})
</script>
<template>
  <div class="listen-container">
    <div class="tip">您好，我在听，请说话</div>
    <div class="ctrlProcessWave"></div>
    <div class="listen-text">{{ props.text }}</div>
  </div>
</template>
<style scoped lang="scss">
.listen-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 50px;
  .tip {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 56px;
    color: #112044;
  }
  .ctrlProcessWave {
    margin-top: 70px;
    width: 100%;
    height: 110px;
  }
  .listen-text {
    margin-top: 80px;
    padding: 0 20px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 32px;
    color: #112044;
  }
}
</style>
