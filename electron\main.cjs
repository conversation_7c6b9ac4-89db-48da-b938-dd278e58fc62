/* eslint-disable no-undef */
const { app, globalShortcut, ipcMain, BrowserWindow, screen, session } = require('electron')
const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')
const os = require('os')
require('dotenv').config()
let win
let keyListenerProcess = null
// let isListening = false

const createWindow = async (x, y) => {
  win = new BrowserWindow({
    x,
    y,
    width: 1080,
    height: 1920,
    frame: false, // 无边框
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: true,
      contextIsolation: true,
      enableRemoteModule: true
    }
  })
  win.show() // 显示并聚焦于窗口
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口
  // 在Electron的主进程中
  const isDev = process.env.ELECTRON_IS_DEV === 'true'
  if (isDev) {
    // 开发环境配置
    win.loadURL(process.env.VITE_DEV_SERVER_URL)

    win.webContents.openDevTools() // 打开开发者工具

    // 指定本地扩展文件路径（需要实际路径）

    try {
      await session.defaultSession.loadExtension(
        path.resolve(__dirname, '../../Vuejs-devtools-6.6.1')
      )
    } catch (err) {
      console.log('Unable to load Vue DevTools111: ', err)
    }
  } else {
    // 生产环境配置
    console.log('Loading file:', path.join(__dirname, '../dist/index.html'))
    // win.loadURL('http://localhost:5173/')
    win.webContents.openDevTools() // 打开开发者工具

    win.loadFile(path.join(__dirname, '../dist/index.html'))
  }
}

// 内嵌的PowerShell脚本内容（作为最后的备用方案）
function getEmbeddedScriptContent() {
  return `# 简化版本的按键监听脚本
Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

public static class SimpleKeyListener
{
    private const int WH_KEYBOARD_LL = 13;
    private const int WM_KEYDOWN = 0x0100;
    private const int WM_KEYUP = 0x0101;
    private const int VK_F2 = 0x71;

    private static LowLevelKeyboardProc _proc = HookCallback;
    private static IntPtr _hookID = IntPtr.Zero;

    public delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    private static extern IntPtr SetWindowsHookEx(int idHook,
        LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool UnhookWindowsHookEx(IntPtr hhk);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode,
        IntPtr wParam, IntPtr lParam);

    [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    private static extern IntPtr GetModuleHandle(string lpModuleName);

    public static IntPtr SetHook(LowLevelKeyboardProc proc)
    {
        using (Process curProcess = Process.GetCurrentProcess())
        using (ProcessModule curModule = curProcess.MainModule)
        {
            return SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                GetModuleHandle(curModule.ModuleName), 0);
        }
    }

    public static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
    {
        if (nCode >= 0)
        {
            int vkCode = Marshal.ReadInt32(lParam);

            if (vkCode == VK_F2)
            {
                if (wParam == (IntPtr)WM_KEYDOWN)
                {
                    Console.WriteLine("KEY_DOWN:F2");
                    Console.Out.Flush();
                }
                else if (wParam == (IntPtr)WM_KEYUP)
                {
                    Console.WriteLine("KEY_UP:F2");
                    Console.Out.Flush();
                }
            }
        }

        return CallNextHookEx(_hookID, nCode, wParam, lParam);
    }

    public static void StartListening()
    {
        _hookID = SetHook(_proc);
        Application.Run();
        UnhookWindowsHookEx(_hookID);
    }
}
"@ -ReferencedAssemblies System.Windows.Forms

try {
    # 不输出启动信息，避免干扰按键事件输出
    [SimpleKeyListener]::StartListening()
} catch {
    # 错误信息输出到stderr，不会干扰stdout
    Write-Error "Error: $($_.Exception.Message)"
    exit 1
}
`
}

// 启动按键监听器
function startKeyListener() {
  if (keyListenerProcess) {
    return
  }

  // 创建临时PowerShell脚本
  const tempDir = os.tmpdir()
  const tempScriptPath = path.join(tempDir, `keyListener_${Date.now()}.ps1`)

  try {
    const scriptContent = getEmbeddedScriptContent()
    fs.writeFileSync(tempScriptPath, scriptContent, 'utf8')
    console.log('Created temp script at:', tempScriptPath)

    keyListenerProcess = spawn(
      'powershell.exe',
      ['-ExecutionPolicy', 'Bypass', '-WindowStyle', 'Hidden', '-File', tempScriptPath],
      {
        stdio: ['pipe', 'pipe', 'pipe']
      }
    )
  } catch (error) {
    console.error('Failed to create temp script:', error)
    console.log('Using global shortcut fallback only')
    return
  }

  keyListenerProcess.stdout.on('data', (data) => {
    console.log('Key event detected:', output)
    const output = data.toString().trim()
    if (output === 'KEY_DOWN:F2') {
      // isListening = true

      console.log('DOWN:', output)
      // 通知所有窗口开始语音监听
      BrowserWindow.getAllWindows().forEach((win) => {
        win.webContents.send('voice-listening', { action: 'start' })
      })
    } else if (output === 'KEY_UP:F2') {
      console.log('UP:', output)

      // isListening = false
      // 通知所有窗口停止语音监听
      BrowserWindow.getAllWindows().forEach((win) => {
        win.webContents.send('voice-listening', { action: 'stop' })
      })
    }
  })

  keyListenerProcess.stderr.on('data', (data) => {
    console.error('Key listener error:', data.toString())
  })

  keyListenerProcess.on('close', (code) => {
    console.log('Key listener process closed with code:', code)
    keyListenerProcess = null
  })

  keyListenerProcess.on('error', (error) => {
    console.error('Failed to start key listener:', error)
    keyListenerProcess = null
  })
}

// 停止按键监听器
function stopKeyListener() {
  if (keyListenerProcess) {
    keyListenerProcess.kill()
    keyListenerProcess = null
  }

  // 清理临时PowerShell脚本文件
  try {
    const tempDir = os.tmpdir()
    const files = fs.readdirSync(tempDir)
    files.forEach((file) => {
      if (file.startsWith('keyListener_') && file.endsWith('.ps1')) {
        const filePath = path.join(tempDir, file)
        fs.unlinkSync(filePath)
        console.log('Cleaned up temp script:', filePath)
      }
    })
  } catch (error) {
    console.error('Error cleaning up temp scripts:', error)
  }
}

function registerGlobalShortcut() {
  // 注册F4快捷键
  const f4Ret = globalShortcut.register('F4', () => {
    console.log('F4 pressed globally!')

    // 通知所有窗口
    BrowserWindow.getAllWindows().forEach((win) => {
      win.webContents.send('global-shortcut', 'F4')
    })
  })

  if (!f4Ret) {
    console.error('F4 registration failed')
  }
  console.log('F4 registered:', globalShortcut.isRegistered('F4'))
}

app.whenReady().then(() => {
  createWindow(1920 + 50, -835 + 50)

  // 启动按键监听器
  startKeyListener()

  // 注册全局快捷键 F4
  registerGlobalShortcut()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('ready', () => {
  // 创建 Electron 窗口等其他操作
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow().webContents.openDevTools()
  })

  // 监听获取屏幕id事件
  ipcMain.handle('get-screen-id', async () => {
    // 获取所有屏幕的信息
    const displays = screen.getAllDisplays()
    // 选择一个屏幕
    const externalDisplay = displays.find((display) => {
      // 你可以根据需要来选择特定的屏幕，例如使用外接屏幕
      return display.bounds.x !== 0 || display.bounds.y !== 0
    })
    return externalDisplay.id
  })
})

// 窗口关闭时注销快捷键
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    globalShortcut.unregisterAll()
    stopKeyListener()
    app.quit()
  }
})

// 应用退出时注销
app.on('will-quit', () => {
  globalShortcut.unregisterAll()
  stopKeyListener()
})
