import{r as w,_ as m,c as p,a as f,b as X,d as o,t as j,w as R,e as C,f as N,g as _,h as P,o as U,i as E,j as h,k as x,v as b}from"./index-BTJj10y7.js";import{u as Z}from"./useListen-Drd5wNNB.js";const F=""+new URL("logo-mWePMNgr.png",import.meta.url).href,K=""+new URL("record-icon-DGktsQP5.png",import.meta.url).href,I="http://192.168.1.223:8010";function V(){const n=w(null),t=w(null),r=w(""),s=w(!0);let e=null;const d=()=>{n.value&&n.value.srcObject&&(n.value.srcObject.getTracks().forEach(a=>a.stop()),n.value.srcObject=null),t.value&&t.value.srcObject&&(t.value.srcObject.getTracks().forEach(a=>a.stop()),t.value.srcObject=null)},i=()=>{e&&(e.ontrack=null,e.onicecandidate=null,e.oniceconnectionstatechange=null,e.onsignalingstatechange=null,e.onicegatheringstatechange=null,e.onconnectionstatechange=null,e.close(),e=null),d(),r.value=""},v=async()=>{var l,a;if(e)try{e.addTransceiver("video",{direction:"recvonly"}),e.addTransceiver("audio",{direction:"recvonly"});const u=await e.createOffer();await e.setLocalDescription(u),await new Promise((L,H)=>{const W=setTimeout(()=>{H(new Error("ICE收集超时"))},1e4);if(e.iceGatheringState==="complete")clearTimeout(W),L();else{const G=()=>{e.iceGatheringState==="complete"&&(e.removeEventListener("icegatheringstatechange",G),clearTimeout(W),L())};e.addEventListener("icegatheringstatechange",G)}});const k=await fetch(`${I}/offer`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sdp:(l=e.localDescription)==null?void 0:l.sdp,type:(a=e.localDescription)==null?void 0:a.type})});if(!k.ok)throw new Error(`服务器响应错误: ${k.status}`);const y=await k.json();if(!y||!y.sdp)throw new Error("服务器返回的answer格式不正确");r.value=y.sessionid,await e.setRemoteDescription(y)}catch(u){throw console.error("协商过程出错:",u),i(),u}},A=async(l=!1)=>{i();const a={sdpSemantics:"unified-plan"};l&&(a.iceServers=[{urls:["stun:stun.l.google.com:19302"]}]),e=new RTCPeerConnection(a),e.onconnectionstatechange=()=>{console.log("连接状态:",e==null?void 0:e.connectionState),(e==null?void 0:e.connectionState)==="connected"&&(s.value=!1),((e==null?void 0:e.connectionState)==="failed"||(e==null?void 0:e.connectionState)==="closed")&&i()},e.oniceconnectionstatechange=()=>{console.log("ICE连接状态:",e==null?void 0:e.iceConnectionState),(e==null?void 0:e.iceConnectionState)==="failed"&&i()},e.ontrack=u=>{u.track.kind==="video"?n.value.srcObject=u.streams[0]:t.value.srcObject=u.streams[0]};try{await v()}catch(u){throw i(),u}},g=()=>{i()};typeof window<"u"&&(window.addEventListener("beforeunload",i),window.addEventListener("unload",i));async function c(l){try{await fetch(`${I}/human`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionid:r.value,type:"echo",text:l})})}catch(a){console.error("发送消息失败:",a)}}return{videoRef:n,audioRef:t,sessionId:r,loading:s,start:A,stop:g,sendMsg:c,interruptDigitalHuman:async()=>{try{if(console.log("开始终止数字人对话..."),r.value)try{await fetch(`${I}/interrupt_talk`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionid:r.value})}),console.log("中断指令已发送到服务器")}catch(l){console.error("发送中断指令失败:",l)}console.log("数字人对话已终止，回到初始状态")}catch(l){console.error("终止数字人对话时出错:",l)}}}}const B={},T={class:"loader"};function z(n,t){return f(),p("div",T,t[0]||(t[0]=[X('<svg viewBox="0 0 100 100" height="100px" width="100px" data-v-c2265296><defs data-v-c2265296><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" id="gradient1" data-v-c2265296><stop stop-color="#4f8ef7" offset="0%" data-v-c2265296></stop><stop stop-color="#a663cc" offset="50%" data-v-c2265296></stop><stop stop-color="#f74f6f" offset="100%" data-v-c2265296></stop></linearGradient><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" id="gradient2" data-v-c2265296><stop stop-color="#f7b34f" offset="0%" data-v-c2265296></stop><stop stop-color="#5ef7a5" offset="50%" data-v-c2265296></stop><stop stop-color="#4f8ef7" offset="100%" data-v-c2265296></stop></linearGradient></defs><circle stroke="url(#gradient1)" r="40" cy="50" cx="50" class="loader-circle circle-1" data-v-c2265296></circle><circle stroke="url(#gradient2)" r="30" cy="50" cx="50" class="loader-circle circle-2" data-v-c2265296></circle></svg>',1)]))}const Q=m(B,[["render",z],["__scopeId","data-v-c2265296"]]),q={class:"listen-container"},J={class:"listen-text"},M={__name:"Listen",props:{text:{type:String,default:""}},setup(n){const t=n;return(r,s)=>(f(),p("div",q,[s[0]||(s[0]=o("div",{class:"tip"},"您好，我在听，请说话",-1)),s[1]||(s[1]=o("div",{class:"ctrlProcessWave"},null,-1)),o("div",J,j(t.text),1)]))}},Y=m(M,[["__scopeId","data-v-9c6b8d5e"]]),O=""+new URL("response-photo-C3K3-GDU.png",import.meta.url).href;function D(){let n=null;const t=w("");async function r(e,d){s();let i=0;return new Promise(v=>{n=setInterval(()=>{i<e.length?(t.value+=e.charAt(i),i++):(clearInterval(n),n=null,v())},d)})}function s(){n&&(clearInterval(n),n=null),t.value=""}return{displayedText:t,startTypeWriterEffect:r,stopTypeWriterEffect:s}}const $={class:"response-container"},ee={class:"response-text"},te={__name:"Response",props:{text:{type:String,default:""},state:{type:String,default:""}},setup(n){const t=n,{displayedText:r,startTypeWriterEffect:s}=D();return R(()=>t.state,(e,d)=>{d==="listening"&&e==="response"&&s(t.text,40)}),(e,d)=>(f(),p("div",$,[o("div",ee,j(C(r)),1),d[0]||(d[0]=o("img",{class:"response-photo",src:O,alt:""},null,-1))]))}},ne=m(te,[["__scopeId","data-v-0889c4bb"]]),oe="data:image/png;base64,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",se={key:0,class:"record-container"},ae={class:"user"},re={class:"ai"},ie={__name:"Record",setup(n){const t=N(()=>_.value[_.value.length-1]);return(r,s)=>t.value?(f(),p("div",se,[o("div",ae,[s[0]||(s[0]=o("img",{src:oe,alt:""},null,-1)),o("span",null,j(t.value.user),1)]),o("div",re,[o("span",null,j(t.value.ai),1),s[1]||(s[1]=o("img",{src:O,alt:""},null,-1))])])):P("",!0)}},ce=m(ie,[["__scopeId","data-v-39a7be6a"]]),le={class:"talk-container"},de={key:0,class:"loading-box"},ue={key:1,class:"record-box"},pe={class:"digitalHuman-box"},fe={class:"operate-box"},ve={class:"tip"},ge={__name:"index",setup(n){const{start:t,stop:r,loading:s,sendMsg:e,videoRef:d,audioRef:i}=V(),{isKeyListening:v,listenText:A,aiResponse:g}=Z(),c=w("tip");R(v,()=>{v.value&&(c.value="listening")}),R(g,async()=>{g.value&&(await e(g.value),c.value="response",A.value="")});function S(){window.electron&&window.electron.onShortcut&&window.electron.onShortcut(()=>{c.value!=="listening"&&(c.value=c.value==="recording"?"response":"recording")})}return U(()=>{t(),S()}),E(()=>{r()}),(l,a)=>(f(),p("div",le,[C(s)?(f(),p("div",de,[h(Q,{class:"loadingComponent"}),a[0]||(a[0]=o("div",null,"数字人正在加载中...",-1))])):P("",!0),a[2]||(a[2]=o("img",{class:"logo",src:F,alt:""},null,-1)),c.value==="response"||c.value==="recording"?(f(),p("div",ue,a[1]||(a[1]=[o("img",{src:K,alt:""},null,-1),o("span",null,"对话记录",-1)]))):P("",!0),o("div",pe,[o("video",{ref_key:"videoRef",ref:d,autoplay:""},null,512),o("audio",{ref_key:"audioRef",ref:i,autoplay:""},null,512)]),o("div",fe,[x(o("div",ve,"按住 F2 键开始语音对话",512),[[b,c.value==="tip"]]),x(h(Y,{text:C(A)},null,8,["text"]),[[b,c.value==="listening"]]),x(h(ne,{text:C(g),state:c.value},null,8,["text","state"]),[[b,c.value==="response"]]),x(h(ce,null,null,512),[[b,c.value==="recording"]])])]))}},Ae=m(ge,[["__scopeId","data-v-9e3961c8"]]);export{Ae as default};
