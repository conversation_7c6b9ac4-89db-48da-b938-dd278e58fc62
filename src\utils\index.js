/**
 * 时间格式化函数
 * @param {Date|string|number} date - 日期对象/日期字符串/时间戳
 * @param {string} format - 格式字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  // 处理输入参数
  if (!date) date = new Date()
  if (typeof date === 'string' || typeof date === 'number') {
    date = new Date(date)
  }

  // 补零函数
  const padZero = (num) => (num < 10 ? `0${num}` : num)

  // 格式化规则映射
  const rules = {
    YYYY: date.getFullYear(),
    MM: padZero(date.getMonth() + 1),
    DD: padZero(date.getDate()),
    HH: padZero(date.getHours()),
    mm: padZero(date.getMinutes()),
    ss: padZero(date.getSeconds()),
    SSS: date.getMilliseconds().toString().padStart(3, '0'),
    YY: date.getFullYear().toString().slice(-2),
    M: date.getMonth() + 1,
    D: date.getDate(),
    H: date.getHours(),
    m: date.getMinutes(),
    s: date.getSeconds()
  }

  // 替换格式化字符串中的占位符
  let result = format
  for (const [key, value] of Object.entries(rules)) {
    result = result.replace(new RegExp(key, 'g'), value)
  }

  return result
}
