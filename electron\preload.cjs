/* eslint-disable no-undef */
// preload.js
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

contextBridge.exposeInMainWorld(
  'electron', // 在页面中访问的名称
  {
    // 通过 IPC 机制异步获取 MAC 地址
    getScreenId: () => ipcRenderer.invoke('get-screen-id'),
    // 也可以暴露其他需要的安全 API
    onShortcut: (callback) => ipcRenderer.on('global-shortcut', callback),
    // 语音监听事件
    onVoiceListening: (callback) => ipcRenderer.on('voice-listening', callback),
    // 移除语音监听事件
    removeVoiceListening: (callback) => ipcRenderer.off('voice-listening', callback)
  }
)
