import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { useAiRequest } from '@/hooks/useAiRequest'
import { useSocketStore } from '@/stores/ws'
import { dialogSystem } from '@/assets/ai-system/system'
const { recOpen, recStart, recStop } = useRecorder()

export function useListen() {
  const socketStore = useSocketStore()
  const { aiRequest } = useAiRequest()
  const listenText = ref('')
  const aiResponse = ref('')

  // 按键录音状态
  const isKeyListening = ref(false)
  const ListeningTimer = ref(null) //防止重复监听

  // 监听 websocket 连接状态
  watch(
    () => socketStore.state,
    async (newVal) => {
      if (newVal === 1) {
        try {
          await recOpen()
        } catch (error) {
          console.error('录音启动失败:', error)
        }
      }
    }
  )

  // 监听 websocket 接收到的文本
  watch(
    () => socketStore.lastData,
    (newData) => {
      if (newData && newData.text) {
        listenText.value = newData.text

        if (newData.isFinish) {
          aiRequestHandle(listenText.value)
        }
      }
    }
  )

  function aiRequestHandle(text = listenText.value) {
    console.log('开始AI请求')
    aiRequest(text, dialogSystem).then((res) => {
      aiResponse.value = res
    })
  }

  // #region 按键监听功能
  // 处理按键监听事件
  const handleVoiceListening = (_, data) => {
    console.log('=== F2按键事件 ===', listenText.value)

    if (data.action === 'start') {
      // 如果已经在监听中，不重复执行初始化
      if (isKeyListening.value) {
        console.log('已经在监听中，忽略重复开始事件')
        return
      }

      isKeyListening.value = true
      console.log('开始按键录音')

      // 检查 WebSocket 连接状态
      if (socketStore.state !== 1) {
        console.warn('WebSocket 未连接，无法开始录音')
        isKeyListening.value = false
        return
      }

      // 如果有停止定时器，说明用户短暂松开后又按下，取消停止操作，继续录音
      if (ListeningTimer.value) {
        clearTimeout(ListeningTimer.value)
        ListeningTimer.value = null
        console.log('取消停止操作，继续录音')
        return // 不需要重新启动录音，因为录音还在进行中
      } else {
        console.log('首次开始录音')
        aiResponse.value = ''
        // 重置消息状态
        socketStore.resetMessage()

        // 直接开始录音
        try {
          recStart()
          console.log('录音已启动')
        } catch (error) {
          console.error('启动录音失败:', error)
          isKeyListening.value = false
        }
      }
    }

    if (data.action === 'stop') {
      console.log('用户松开F2，当前监听状态:', isKeyListening.value)

      if (!isKeyListening.value) {
        console.log('当前未在监听状态，忽略停止事件')
        return
      }

      isKeyListening.value = false

      console.log('设置400ms延迟停止录音')
      ListeningTimer.value = setTimeout(() => {
        console.log('执行延迟停止录音')
        // 停止录音
        recStop()
        clearTimeout(ListeningTimer.value)
        ListeningTimer.value = null
      }, 400)
    }
  }

  // 初始化按键监听
  onMounted(() => {
    // 监听来自主进程的按键事件
    if (window.electron && window.electron.onVoiceListening) {
      window.electron.onVoiceListening(handleVoiceListening)
    }
  })

  // 清理按键监听
  onUnmounted(() => {
    // 清理按键监听事件
    if (window.electron && window.electron.removeVoiceListening) {
      window.electron.removeVoiceListening(handleVoiceListening)
    }
  })

  // #endregion
  socketStore.wsStart()
  return {
    listenText,
    isKeyListening,
    aiResponse
  }
}
