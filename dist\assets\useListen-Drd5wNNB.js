import{a4 as g,r,w as l,a5 as w,p as S,a6 as d,o as m}from"./index-BTJj10y7.js";const{recOpen:p,recStart:h,recStop:L}=w();function R(){const s=g(),{aiRequest:c}=S(),n=r(""),a=r(""),i=r(!1),t=r(null);l(()=>s.state,async e=>{if(e===1)try{await p()}catch(o){console.error("录音启动失败:",o)}}),l(()=>s.lastData,e=>{e&&e.text&&(n.value=e.text,e.isFinish&&u(n.value))});function u(e=n.value){console.log("开始AI请求"),c(e,d).then(o=>{a.value=o})}const f=(e,o)=>{if(o.action==="start"){if(i.value)return;if(i.value=!0,console.log("开始按键录音"),s.state!==1){console.warn("WebSocket 未连接，无法开始录音");return}if(t.value){clearTimeout(t.value),t.value=null;return}else{a.value="",s.resetMessage();try{h(),console.log("录音已启动")}catch(v){console.error("启动录音失败:",v)}}}else o.action==="stop"&&(console.log("用户松开F2"),i.value=!1,t.value=setTimeout(()=>{L(),clearTimeout(t.value),t.value=null},400))};return m(()=>{window.electron&&window.electron.onVoiceListening&&window.electron.onVoiceListening(f)}),s.wsStart(),{listenText:n,isKeyListening:i,aiResponse:a}}export{R as u};
